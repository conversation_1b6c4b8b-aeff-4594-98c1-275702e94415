import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { showToast, GlobalContext } from "Context/Global";
import { useContext } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

const CrownIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="42"
    height="33"
    viewBox="0 0 42 33"
    fill="none"
  >
    <path
      d="M22.4766 5.79688C23.2781 5.30469 23.8125 4.41172 23.8125 3.40625C23.8125 1.85234 22.5539 0.59375 21 0.59375C19.4461 0.59375 18.1875 1.85234 18.1875 3.40625C18.1875 4.41875 18.7219 5.30469 19.5234 5.79688L15.4945 13.8547C14.8547 15.1344 13.1953 15.5 12.0773 14.607L5.8125 9.59375C6.16406 9.12266 6.375 8.53906 6.375 7.90625C6.375 6.35234 5.11641 5.09375 3.5625 5.09375C2.00859 5.09375 0.75 6.35234 0.75 7.90625C0.75 9.46016 2.00859 10.7188 3.5625 10.7188C3.57656 10.7188 3.59766 10.7188 3.61172 10.7188L6.825 28.3953C7.21172 30.5328 9.075 32.0938 11.2547 32.0938H30.7453C32.918 32.0938 34.7812 30.5398 35.175 28.3953L38.3883 10.7188C38.4023 10.7188 38.4234 10.7188 38.4375 10.7188C39.9914 10.7188 41.25 9.46016 41.25 7.90625C41.25 6.35234 39.9914 5.09375 38.4375 5.09375C36.8836 5.09375 35.625 6.35234 35.625 7.90625C35.625 8.53906 35.8359 9.12266 36.1875 9.59375L29.9227 14.607C28.8047 15.5 27.1453 15.1344 26.5055 13.8547L22.4766 5.79688Z"
      fill="#7DD87D"
    />
  </svg>
);

const CreateCommunityPage = () => {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const navigate = useNavigate();
  const { id } = useParams(); // Get community ID from URL if we're editing
  const isEditMode = !!id;
  const [activeTab, setActiveTab] = useState("basic");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(isEditMode); // Start loading if in edit mode
  const [communityMembers, setCommunityMembers] = useState([]);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    linkedinProfile: "",
    websiteUrl: "",
    additionalInfo: "",
    description: "",
    guidelines: "",
    title: "",
    industry: "",
    banner: "",
    who_can_find: "everyone",
    who_can_join: "anyone",
    who_can_see_content: "everyone",
    who_can_invite: "everyone",
    who_can_post: "everyone",
    view_list: "everyone",
    activity_visibility: "public",
    group_activity_visibility: "show",
    content_moderation: "admin_approval",
    community_moderator: "",
    admins: "",
    subscription_fee: "0",
    enable_affiliate: false,
  });
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [bannerPreview, setBannerPreview] = useState("");
  const [uploadingBanner, setUploadingBanner] = useState(false);

  // Add this useEffect to load community data if in edit mode
  useEffect(() => {
    if (isEditMode) {
      loadCommunityData();
      fetchCommunityMembers();
    }
  }, [id]);

  // Add useEffect to fetch user details when component mounts
  useEffect(() => {
    const fetchUserDetails = async () => {
      try {
        const sdk = new MkdSDK();
        const response = await sdk.callRawAPI(
          "/v1/api/dealmaker/user/details",
          {},
          "GET"
        );

        if (!response.error && response.model) {
          setFormData(prev => ({
            ...prev,
            firstName: response.model.first_name.value || "",
            lastName: response.model.last_name.value || ""
          }));
        }
      } catch (err) {
        console.error("Failed to fetch user details:", err);
        setError("Failed to fetch user details");
      }
    };

    // Only fetch user details if not in edit mode
    if (!isEditMode) {
      fetchUserDetails();
    }
  }, [isEditMode]);
  console.log("community members", communityMembers);
  const sdk = new MkdSDK();

  const fetchCommunityMembers = async () => {
    try {
      const sdk = new MkdSDK();
      // For edit mode, fetch community members
      if (isEditMode) {
        const response = await sdk.callRawAPI(
          `/v1/api/dealmaker/user/community/${id}/users`,
          {},
          "GET"
        );

        if (!response.error && response.list) {
          setCommunityMembers(response.list);
        }
      }
    } catch (err) {
      console.error("Failed to fetch community members:", err);
      showToast(
        globalDispatch,
        "Failed to fetch community members",
        5000,
        "error"
      );
    }
  };

  // Update the useEffect to depend on isEditMode
  useEffect(() => {
    if (isEditMode) {
      fetchCommunityMembers();
    }
  }, [isEditMode, id]);

  const loadCommunityData = async () => {
    try {
      console.log("Loading community data for ID:", id);
      const sdk = new MkdSDK();
      const response = await sdk.GetCommunityDetail(id);

      console.log("API Response:", response);

      if (!response.error) {
        const community = response.model;
        console.log("Community data received:", community);

        // Update form data with community values
        const newFormData = {
          description: community.description?.value || "",
          guidelines: community.guidelines?.value || "",
          title: community.title?.value || "",
          banner: community.banner?.value || "",
          industry: community.industry_name?.value || "",
          firstName: community?.user_details?.value?.first_name?.value || "",
          lastName: community?.user_details?.value?.last_name?.value || "",
          linkedinProfile:
            community?.user_details?.value?.linkedin_profile?.value || "",
          websiteUrl: community?.user_details?.value?.website_url?.value || "",
          additionalInfo:
            community?.user_details?.value?.additional_info?.value || "",
          who_can_find:
            community.privacy_settings?.value?.who_can_find || "everyone",
          who_can_join:
            community.privacy_settings?.value?.who_can_join || "anyone",
          who_can_see_content:
            community.privacy_settings?.value?.who_can_see_content ||
            "everyone",
          who_can_invite:
            community.privacy_settings?.value?.who_can_invite || "everyone",
          who_can_post:
            community.privacy_settings?.value?.who_can_post || "everyone",
          view_list: community.privacy_settings?.value?.view_list || "everyone",
          activity_visibility:
            community.privacy_settings?.value?.activity_visibility || "public",
          group_activity_visibility:
            community.privacy_settings?.value?.group_activity_visibility ||
            "show",
          content_moderation:
            community.privacy_settings?.value?.content_moderation ||
            "admin_approval",
          community_moderator:
            community.privacy_settings?.value?.community_moderator || "",
          admins: community.privacy_settings?.value?.admins || "",
          subscription_fee:
            community.privacy_settings?.value?.subscription_fee || "0",
          enable_affiliate:
            community.privacy_settings?.value?.enable_affiliate || false,
        };

        console.log("Setting form data to:", newFormData);
        setFormData(newFormData);
      } else {
        console.error("Failed to load community data:", response.error);
        showToast(
          globalDispatch,
          "Failed to load community data",
          5000,
          "error"
        );
      }
    } catch (err) {
      console.error("Error in loadCommunityData:", err);
      showToast(
        globalDispatch,
        err.message || "Failed to load community data",
        5000,
        "error"
      );
    } finally {
      setLoading(false);
    }
  };

  const validateBasicInfo = () => {
    const newErrors = {};
    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateDescription = () => {
    const newErrors = {};
    if (!formData.title.trim()) {
      newErrors.title = "Community title is required";
    }
    if (!formData.industry.trim()) {
      newErrors.industry = "Industry is required";
    }
    if (!formData.description.trim()) {
      newErrors.description = "Community description is required";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateGuidelines = () => {
    const newErrors = {};
    if (!formData.guidelines.trim()) {
      newErrors.guidelines = "Community guidelines are required";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validatePrivacySettings = () => {
    console.log("Validating privacy settings with formData:", formData);
    const newErrors = {};

    // Use the snake_case field names that match formData
    if (!formData.who_can_find) {
      newErrors.who_can_find = "Please select who can find the community";
    }
    if (!formData.who_can_join) {
      newErrors.who_can_join = "Please select who can join the community";
    }

    console.log("Validation errors:", newErrors);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNextStep = () => {
    let isValid = false;
    switch (activeTab) {
      case "basic":
        isValid = validateBasicInfo();
        break;
      case "description":
        isValid = validateDescription();
        break;
      case "guidelines":
        isValid = validateGuidelines();
        break;
      case "privacy":
        isValid = validatePrivacySettings();
        break;
    }

    if (isValid) {
      const tabs = ["basic", "description", "guidelines", "privacy"];
      const currentIndex = tabs.indexOf(activeTab);
      setActiveTab(tabs[currentIndex + 1]);
      setErrors({});
    }
  };

  const handleFormSubmit = async () => {
    try {
      console.log("Form submit started with formData:", formData);

      if (!validatePrivacySettings()) {
        console.log("Privacy settings validation failed");
        return;
      }

      // Validate required fields
      if (!formData.title) {
        showToast(globalDispatch, "Community title is required", 5000, "error");
        setActiveTab("description");
        return;
      }
      if (!formData.industry) {
        showToast(globalDispatch, "Industry is required", 5000, "error");
        setActiveTab("description");
        return;
      }
      if (!formData.description) {
        showToast(
          globalDispatch,
          "Community description is required",
          5000,
          "error"
        );
        setActiveTab("description");
        return;
      }
      if (!formData.guidelines) {
        showToast(
          globalDispatch,
          "Community guidelines are required",
          5000,
          "error"
        );
        setActiveTab("guidelines");
        return;
      }

      console.log(
        "All validations passed, proceeding with",
        isEditMode ? "update" : "create"
      );

      // If editing, submit now. If creating, show payment modal
      if (isEditMode) {
        await handleUpdateCommunity();
      } else {
        setShowPaymentModal(true);
      }
    } catch (err) {
      console.error("Error in handleFormSubmit:", err);
      showToast(
        globalDispatch,
        err.message || "Failed to process community",
        5000,
        "error"
      );
    }
  };

  const updateUserRole = async (userId, role, communityId) => {
    try {
      const sdk = new MkdSDK();
      const roleUpdatePayload = {
        user_id: { value: userId },
        remove: { value: false },
        role: { value: role },
      };

      await sdk.callRawAPI(
        `/v1/api/dealmaker/user/community/${communityId}/update`,
        roleUpdatePayload,
        "POST"
      );
    } catch (err) {
      console.error(`Failed to update role for user ${userId}:`, err);
      throw err;
    }
  };

  const handleUpdateCommunity = async () => {
    try {
      console.log("Starting community update...");
      console.log("Current form data:", formData);

      setLoading(true);
      setError("");
      const sdk = new MkdSDK();

      // Update payload without role assignments
      const updateData = {
        title: { value: formData.title },
        description: { value: formData.description },
        guidelines: { value: formData.guidelines },
        industry: { value: formData.industry },
        first_name: { value: formData.firstName },
        last_name: { value: formData.lastName },
        linkedin_profile: { value: formData.linkedinProfile },
        website_url: { value: formData.websiteUrl },
        additional_info: { value: formData.additionalInfo },
        privacy_settings: {
          value: {
            who_can_invite: formData.who_can_invite,
            activity_visibility: formData.activity_visibility,
            who_can_post: formData.who_can_post,
            view_list: formData.view_list,
            who_can_find: formData.who_can_find,
            who_can_join: formData.who_can_join,
            who_can_see_content: formData.who_can_see_content,
            content_moderation: formData.content_moderation,
            enable_affiliate: formData.enable_affiliate,
            subscription_fee: formData.subscription_fee,
            // Remove moderator and admin assignments from initial update
          },
        },
      };

      console.log("Update payload:", updateData);
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/community/${id}/edit`,
        updateData,
        "PUT"
      );
      console.log("Update response:", response);

      if (response.error) {
        console.error("Update failed:", response.message);
        showToast(globalDispatch, response.message, 5000, "error");
      } else {
        showToast(
          globalDispatch,
          "Community updated successfully!",
          5000,
          "success"
        );
        navigate(`/member/communities`);
      }
    } catch (err) {
      console.error("Error in handleUpdateCommunity:", err);
      showToast(
        globalDispatch,
        err.message || "Failed to update community",
        5000,
        "error"
      );
    } finally {
      setLoading(false);
    }
  };

  const pageTitle = isEditMode ? "Edit Community" : "Create a Community";
  const submitButtonText = isEditMode ? "Save Changes" : "Create Community";

  const handleCreateCommunity = async (skipPayment = false) => {
    try {
      console.log("Starting community creation...");
      console.log("Current form data:", formData);

      setPaymentLoading(true);
      setError("");
      const sdk = new MkdSDK();

      // Create payload with nested value structure
      const payload = {
        title: { value: formData.title },
        description: { value: formData.description },
        guidelines: { value: formData.guidelines },
        industry: { value: formData.industry },
        logo: { value: formData.banner },
        first_name: { value: formData.firstName },
        last_name: { value: formData.lastName },
        linkedin_profile: { value: formData.linkedinProfile },
        website_url: { value: formData.websiteUrl },
        additional_info: { value: formData.additionalInfo },
        skip_payment: { value: skipPayment },
        privacy_settings: {
          value: {
            who_can_invite: formData.who_can_invite,
            activity_visibility: formData.activity_visibility,
            who_can_post: formData.who_can_post,
            view_list: formData.view_list,
            who_can_find: formData.who_can_find,
            who_can_join: formData.who_can_join,
            who_can_see_content: formData.who_can_see_content,
            content_moderation: formData.content_moderation,
            enable_affiliate: formData.enable_affiliate,
            subscription_fee: formData.subscription_fee,
            // Remove moderator and admin assignments from initial creation
          },
        },
      };

      console.log("Create payload:", payload);
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/community/create`,
        payload,
        "POST"
      );
      console.log("Create response:", response);

      if (response.error) {
        console.error("Creation failed:", response.message);
        showToast(globalDispatch, response.message, 5000, "error");
      } else {
        // Community created successfully
        showToast(
          globalDispatch,
          "Community created successfully!",
          5000,
          "success"
        );
        navigate(`/member/communities`);
      }
    } catch (err) {
      console.error("Error in handleCreateCommunity:", err);
      showToast(
        globalDispatch,
        err.message || "Failed to create community",
        5000,
        "error"
      );
    } finally {
      setPaymentLoading(false);
      setShowPaymentModal(false);
    }
  };

  const modules = {
    toolbar: {
      container: [
        ["bold", "italic", "underline"],
        [{ list: "bullet" }, { list: "ordered" }],
        ["link", "image", "code-block"],
        [{ align: [] }],
      ],
    },
  };

  const formats = [
    "bold",
    "italic",
    "underline",
    "list",
    "bullet",
    "link",
    "image",
    "code-block",
    "align",
  ];

  const handleBannerUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
      setUploadingBanner(true);
      const sdk = new MkdSDK();
      const formData = new FormData();
      formData.append("file", file);

      console.log("hi", formData);

      const result = await sdk.uploadImage(formData);
      console.log(result);
      if (result.url) {
        setFormData((prev) => ({ ...prev, banner: result.url }));
        setBannerPreview(URL.createObjectURL(file));
        showToast(
          globalDispatch,
          "Banner uploaded successfully",
          5000,
          "success"
        );
      }
    } catch (err) {
      console.error("Error uploading banner:", err);
      showToast(globalDispatch, "Failed to upload banner", 5000, "error");
    } finally {
      setUploadingBanner(false);
    }
  };

  const renderBasicInfo = () => (
    <div
      style={{
        backgroundColor: "#1e1e1e",
        padding: "16px",
      }}
      className="space-y-6"
    >
      {/* Banner Upload Section */}
      <div className="mb-6">
        <label
          style={{
            marginBottom: "8px",
            color: "#B5B5B5",
            fontSize: "11px",
          }}
          className="mb-2 block text-sm text-[#eaeaea]"
        >
          Community Banner
        </label>
        <div className="relative">
          <div
            className={`flex h-[200px] w-full items-center justify-center rounded-lg border-2 border-dashed ${
              uploadingBanner ? "border-[#7dd87d]" : "border-[#363636]"
            } bg-black transition-all hover:border-[#7dd87d]`}
          >
            {bannerPreview ? (
              <div className="relative w-full h-full">
                <img
                  src={bannerPreview}
                  alt="Banner preview"
                  className="object-cover w-full h-full rounded-lg"
                />
                <button
                  onClick={() => {
                    setFormData((prev) => ({ ...prev, banner: "" }));
                    setBannerPreview("");
                  }}
                  className="absolute top-2 right-2 p-1 text-white rounded-full bg-black/50 hover:bg-black/70"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            ) : (
              <div className="text-center">
                {uploadingBanner ? (
                  <div className="flex flex-col items-center">
                    <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-[#7dd87d]"></div>
                    <p className="mt-2 text-sm text-[#7dd87d]">Uploading...</p>
                  </div>
                ) : (
                  <>
                    <svg
                      className="mx-auto h-12 w-12 text-[#666]"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                    <p className="mt-2 text-sm text-[#666]">
                      Click to upload or drag and drop
                    </p>
                    <p className="mt-1 text-xs text-[#666]">
                      PNG, JPG, GIF up to 10MB
                    </p>
                  </>
                )}
              </div>
            )}
            <input
              type="file"
              accept="image/*"
              onChange={handleBannerUpload}
              disabled={uploadingBanner}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div>
          <label
            style={{
              marginBottom: "8px",
              color: "#B5B5B5",
              fontSize: "11px",
            }}
            className="mb-2 block text-sm text-[#eaeaea]"
          >
            First Name *
          </label>
          <input
            type="text"
            className={`h-11 w-full rounded-lg border ${
              errors.firstName ? "border-red-500" : "border-[#363636]"
            } bg-black px-4 text-sm text-[#eaeaea] placeholder-[#666]`}
            placeholder="Enter your first name"
            value={formData.firstName}
            onChange={(e) =>
              setFormData({ ...formData, firstName: e.target.value })
            }
          />
          {errors.firstName && (
            <p className="mt-1 text-xs text-red-500">{errors.firstName}</p>
          )}
        </div>
        <div>
          <label
            style={{
              marginBottom: "8px",
              color: "#B5B5B5",
              fontSize: "11px",
            }}
            className="mb-2 block text-sm text-[#eaeaea]"
          >
            Last Name *
          </label>
          <input
            type="text"
            className={`h-11 w-full rounded-lg border ${
              errors.lastName ? "border-red-500" : "border-[#363636]"
            } bg-black px-4 text-sm text-[#eaeaea] placeholder-[#666]`}
            placeholder="Enter your last name"
            value={formData.lastName}
            onChange={(e) =>
              setFormData({ ...formData, lastName: e.target.value })
            }
          />
          {errors.lastName && (
            <p className="mt-1 text-xs text-red-500">{errors.lastName}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div>
          <label
            style={{
              marginBottom: "8px",
              marginTop: "12px",
              color: "#B5B5B5",
              fontSize: "11px",
            }}
            className="mb-2 block text-sm text-[#eaeaea]"
          >
            LinkedIn Profile
          </label>
          <div className="relative">
            <div className="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
              <svg
                className="h-5 w-5 text-[#666666]"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14m-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93h2.79M6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 0 0-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37h2.77z" />
              </svg>
            </div>
            <input
              type="text"
              className="h-[44px] w-full rounded-lg border border-[#363636] bg-black pl-10 pr-4 text-sm text-[#eaeaea] placeholder-[#666666]"
              placeholder="https://linkedin.com/in/username"
              value={formData.linkedinProfile}
              onChange={(e) =>
                setFormData({ ...formData, linkedinProfile: e.target.value })
              }
            />
          </div>
        </div>
        <div>
          <label
            style={{
              marginBottom: "8px",
              marginTop: "12px",
              color: "#B5B5B5",
              fontSize: "11px",
            }}
            className="mb-2 block text-sm text-[#eaeaea]"
          >
            Website URL
          </label>
          <div className="relative">
            <div className="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
              <svg
                className="h-5 w-5 text-[#666666]"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
                />
              </svg>
            </div>
            <input
              type="text"
              className="h-[44px] w-full rounded-lg border border-[#363636] bg-black pl-10 pr-4 text-sm text-[#eaeaea] placeholder-[#666666]"
              placeholder="https://yourwebsite.com"
              value={formData.websiteUrl}
              onChange={(e) =>
                setFormData({ ...formData, websiteUrl: e.target.value })
              }
            />
          </div>
        </div>
      </div>

      <div>
        <label
          style={{
            marginBottom: "8px",
            marginTop: "12px",
            color: "#B5B5B5",
            fontSize: "11px",
          }}
          className="mb-2 block text-sm text-[#eaeaea]"
        >
          Additional Information
        </label>
        <textarea
          className="min-h-[120px] w-full rounded-lg border border-[#363636] bg-black p-4 text-sm text-[#eaeaea] placeholder-[#666]"
          placeholder="Share any additional information about yourself or your community..."
          value={formData.additionalInfo}
          onChange={(e) =>
            setFormData({ ...formData, additionalInfo: e.target.value })
          }
        />
      </div>
    </div>
  );

  const renderDescription = () => (
    <div className="space-y-6">
      <div>
        <label
          style={{
            marginBottom: "8px",
            marginTop: "8px",
            color: "#B5B5B5",
            fontSize: "11px",
          }}
          className="mb-2 block text-sm text-[#eaeaea]"
        >
          Community Title *
        </label>
        <input
          type="text"
          className={`h-[44px] w-full rounded-lg border ${
            errors.title ? "border-red-500" : "border-[#363636]"
          } bg-black px-4 text-sm text-[#eaeaea] placeholder-[#666666]`}
          placeholder="Enter community title"
          value={formData.title}
          onChange={(e) => setFormData({ ...formData, title: e.target.value })}
        />
        {errors.title && (
          <p className="mt-1 text-xs text-red-500">{errors.title}</p>
        )}
      </div>

      <div>
        <label
          style={{
            marginBottom: "8px",
            marginTop: "12px",
            color: "#B5B5B5",
            fontSize: "11px",
          }}
          className="mb-2 block text-sm text-[#eaeaea]"
        >
          Industry *
        </label>
        <div className="relative">
          <select
            style={{
              color: "#B5B5B5",
              fontSize: "11px",
            }}
            className={`h-[44px] w-full rounded-lg border ${
              errors.industry ? "border-red-500" : "border-[#363636]"
            } cursor-pointer appearance-none bg-black px-4`}
            value={formData.industry}
            onChange={(e) =>
              setFormData({ ...formData, industry: e.target.value })
            }
          >
            <option value="" disabled selected>
              Select industry
            </option>
            <option value="agriculture_farming">Agriculture & Farming</option>
            <option value="construction">Construction</option>
            <option value="education_training">Education & Training</option>
            <option value="energy_utilities">Energy & Utilities</option>
            <option value="financial_services">Financial Services</option>
            <option value="government_public_sector">Government & Public Sector</option>
            <option value="healthcare_life_sciences">Healthcare & Life Sciences</option>
            <option value="hospitality_tourism">Hospitality & Tourism</option>
            <option value="information_technology_software">Information Technology & Software</option>
            <option value="legal_services">Legal Services</option>
            <option value="logistics_transportation">Logistics & Transportation</option>
            <option value="manufacturing">Manufacturing</option>
            <option value="marketing_advertising">Marketing & Advertising</option>
            <option value="media_entertainment">Media & Entertainment</option>
            <option value="non_profit_charities">Non-Profit & Charities</option>
            <option value="professional_services">Professional Services (e.g., consulting, accounting)</option>
            <option value="real_estate_property_management">Real Estate & Property Management</option>
            <option value="retail_e_commerce">Retail & E-Commerce</option>
            <option value="telecommunications">Telecommunications</option>
            <option value="wholesale_distribution">Wholesale & Distribution</option>
          </select>
          {errors.industry && (
            <p className="mt-1 text-xs text-red-500">{errors.industry}</p>
          )}
        </div>
      </div>

      <div>
        <label
          style={{
            marginBottom: "8px",
            marginTop: "12px",
            color: "#B5B5B5",
            fontSize: "11px",
          }}
          className="mb-2 block text-sm text-[#eaeaea]"
        >
          Community Description *
        </label>
        <textarea
          className={`w-full rounded-lg border ${
            errors.description ? "border-red-500" : "border-[#363636]"
          } min-h-[120px] resize-none bg-black p-4 text-sm text-[#eaeaea] placeholder-[#666666]`}
          placeholder="Describe your community..."
          value={formData.description}
          onChange={(e) =>
            setFormData({ ...formData, description: e.target.value })
          }
        />
        {errors.description && (
          <p className="mt-1 text-xs text-red-500">{errors.description}</p>
        )}
      </div>
    </div>
  );

  const renderGuidelines = () => (
    <div className="space-y-6">
      <div>
        <label
          style={{
            marginBottom: "8px",
            marginTop: "12px",
            color: "#B5B5B5",
            fontSize: "11px",
          }}
          className="mb-2 block text-sm text-[#eaeaea]"
        >
          Community Guidelines *
        </label>
        <div
          className={`editor-wrapper ${
            errors.guidelines ? "border-red-500" : ""
          }`}
        >
          <ReactQuill
            theme="snow"
            value={formData.guidelines}
            onChange={(content) =>
              setFormData({ ...formData, guidelines: content })
            }
            modules={modules}
            formats={formats}
            placeholder="Enter your community guidelines here..."
            className="rounded-lg bg-[#1e1e1e] text-sm text-[#eaeaea]"
          />
        </div>
        {errors.guidelines && (
          <p className="mt-1 text-xs text-red-500">{errors.guidelines}</p>
        )}
      </div>
    </div>
  );

  // Helper function to determine which options should be available based on current selections
  const getAvailableOptions = (field) => {
    const { who_can_find, who_can_join } = formData;

    // If community is private or hidden, certain options should be restricted
    const isPrivateOrHidden = who_can_find === 'private' || who_can_find === 'hidden';

    switch (field) {
      case 'who_can_see_content':
        // If community is private/hidden, content can't be visible to everyone
        return isPrivateOrHidden
          ? [{ value: 'members_only', label: 'Members Only' }]
          : [
              { value: 'everyone', label: 'Everyone' },
              { value: 'members_only', label: 'Members Only' }
            ];

      case 'who_can_join':
        // If community is hidden or private, joining should be more restricted
        return who_can_find === 'hidden' || who_can_find === 'private'
          ? [
              { value: 'request_approval', label: 'Request Approval' },
              { value: 'invite_only', label: 'Invite Only' }
            ]
          : [
              { value: 'anyone', label: 'Anyone' },
              { value: 'request_approval', label: 'Request Approval' },
              { value: 'invite_only', label: 'Invite Only' }
            ];

      case 'view_list':
        // If community is private/hidden, member list shouldn't be visible to everyone
        return isPrivateOrHidden
          ? [
            { value: 'everyone', label: 'Everyone' },
            { value: 'members_only', label: 'Members Only' },
            { value: 'admin', label: 'Admins Only' }
            ]
          : [
              { value: 'everyone', label: 'Everyone' },
              { value: 'members_only', label: 'Members Only' },
              { value: 'admin', label: 'Admins Only' }
            ];

      case 'activity_visibility':
        // If community is private/hidden, activity shouldn't be visible to everyone
        return isPrivateOrHidden
          ? [
              { value: 'admin', label: 'Admin Updates Only' },
              { value: 'custom', label: 'Custom' }
            ]
          : [
              { value: 'everyone', label: 'Everyone' },
              { value: 'admin', label: 'Admin Updates Only' },
              { value: 'custom', label: 'Custom' }
            ];

      case 'who_can_post':
        // If join is invite_only, posting might be more restricted
        return who_can_join === 'invite_only'
          ? [{ value: 'admin', label: 'Admins Only' }]
          : [
              { value: 'admin', label: 'Admins Only' },
              { value: 'everyone', label: 'Everyone' }
            ];

      default:
        return [];
    }
  };

  // Function to handle privacy setting changes with automatic adjustments
  const handlePrivacyChange = (field, value) => {
    const newFormData = { ...formData, [field]: value };

    // Auto-adjust incompatible settings
    if (field === 'who_can_find' && (value === 'private' || value === 'hidden')) {
      // If community becomes private/hidden, adjust other settings
      if (newFormData.who_can_see_content === 'everyone') {
        newFormData.who_can_see_content = 'members_only';
      }

      if (newFormData.view_list === 'everyone') {
        newFormData.view_list = 'members_only';
      }

      if (newFormData.activity_visibility === 'everyone') {
        newFormData.activity_visibility = 'admin';
      }

      // If hidden or private, joining can't be 'anyone'
      if ((value === 'hidden' || value === 'private') && newFormData.who_can_join === 'anyone') {
        newFormData.who_can_join = 'invite_only';
      }
    }

    // If join is invite_only, posting should be admin-only
    if (field === 'who_can_join' && value === 'invite_only') {
      newFormData.who_can_post = 'admin';
    }

    setFormData(newFormData);
  };

  const renderPrivacySettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-4">
          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">
              Who can find the community?
            </label>
            <select
              value={formData.who_can_find}
              onChange={(e) => handlePrivacyChange('who_can_find', e.target.value)}
              className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
            >
              <option value="public">Public</option>
              <option value="hidden">Hidden</option>
              <option value="private">Private</option>
            </select>
          </div>

          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">
              Who Can See community Content?
            </label>
            <select
              value={formData.who_can_see_content}
              onChange={(e) => handlePrivacyChange('who_can_see_content', e.target.value)}
              className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
            >
              {getAvailableOptions('who_can_see_content').map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {formData.who_can_find !== 'public' && formData.who_can_see_content === 'everyone' && (
              <p className="mt-1 text-xs text-amber-500">
                Note: Hidden and private communities can only be shown to members
              </p>
            )}
          </div>

          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">
              Who Can Invite New Members?
            </label>
            <select
              value={formData.who_can_invite}
              onChange={(e) => handlePrivacyChange('who_can_invite', e.target.value)}
              className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
            >
              <option value="admin">Admins Only</option>
              <option value="everyone">All Members</option>
            </select>
          </div>

          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">
              Content Moderation Settings
            </label>
            <select
              value={formData.content_moderation}
              onChange={(e) => handlePrivacyChange('content_moderation', e.target.value)}
              className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
            >
              <option value="admin_approval">Admin Approval Required</option>
              <option value="keyword_blocking">Keyword Blocking</option>
              <option value="reported_content">Reported Content Review</option>
            </select>
          </div>

          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">
              Activity Visibility
            </label>
            <select
              value={formData.activity_visibility}
              onChange={(e) => handlePrivacyChange('activity_visibility', e.target.value)}
              className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
            >
              {getAvailableOptions('activity_visibility').map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>



          {isEditMode && <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">
              Select community Moderator
            </label>
            <select
              value={formData.community_moderator}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  community_moderator: e.target.value,
                })
              }
              className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#161616] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]"
            >
              <option value="">Select moderator</option>
              {communityMembers.map((member) => (
                <option key={member.id} value={member.id.value}>
                  {member.name.value}
                </option>
              ))}
            </select>
          </div>}
        </div>

        {/* Right Column */}
        <div className="space-y-4">
          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">
              Who Can Join the community?
            </label>
            <select
              value={formData.who_can_join}
              onChange={(e) => handlePrivacyChange('who_can_join', e.target.value)}
              className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
            >
              {getAvailableOptions('who_can_join').map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {(formData.who_can_find === 'hidden' || formData.who_can_find === 'private') && formData.who_can_join === 'anyone' && (
              <p className="mt-1 text-xs text-amber-500">
                Hidden and private communities require invitation or approval to join
              </p>
            )}
          </div>

          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">
              Posting Permissions
            </label>
            <select
              value={formData.who_can_post}
              onChange={(e) => handlePrivacyChange('who_can_post', e.target.value)}
              className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
            >
              {getAvailableOptions('who_can_post').map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {formData.who_can_join === 'invite_only' && formData.who_can_post === 'everyone' && (
              <p className="mt-1 text-xs text-amber-500">
                Invite-only communities restrict posting to admins
              </p>
            )}
          </div>

          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">
              Who Can View the Member List?
            </label>
            <select
              value={formData.view_list}
              onChange={(e) => handlePrivacyChange('view_list', e.target.value)}
              className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
            >
              {getAvailableOptions('view_list').map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">
              Activity Visibility
            </label>
            <select
              value={formData.group_activity_visibility}
              onChange={(e) => handlePrivacyChange('group_activity_visibility', e.target.value)}
              className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
            >
              <option value="show">Show Group Activity</option>
              <option value="hide">Hide Group Activity</option>
            </select>
          </div>

         {/* {isEditMode &&  <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">
              Select admins
            </label>
            <select
              value={formData.admins}
              onChange={(e) =>
                setFormData({ ...formData, admins: e.target.value })
              }
              className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#161616] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]"
            >
              <option value="">Select admins</option>
              {communityMembers.map((member) => (
                <option key={member.id} value={member.id.value}>
                  {member.name.value}
                </option>
              ))}
            </select>
          </div>} */}

          <div>
            <label className="mb-2 block text-sm text-[#b5b5b5]">
              Add monthly Charge for Monthly subscription
            </label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-[#eaeaea]">
                $
              </span>
              <input
                type="number"
                min="0"
                step="0.01"
                value={formData.subscription_fee}
                onChange={(e) =>
                  setFormData({ ...formData, subscription_fee: e.target.value })
                }
                className="h-10 w-full rounded-lg border border-[#363636] bg-[#161616] pl-8 pr-4 text-[#eaeaea]"
                placeholder="50.00"
              />
            </div>
            <div className="mt-3">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.enable_affiliate}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      enable_affiliate: e.target.checked
                    })
                  }
                  className="w-4 h-4 rounded border-[#363636] bg-[#161616] text-[#2e7d32] focus:ring-[#2e7d32]"
                />
                <span className="ml-2 text-sm text-[#eaeaea]">
                  Enable Affiliates
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const editorStyles = `
    .editor-wrapper {
      background: #1e1e1e;
      border-radius: 8px;
      border: 1px solid #363636;
    }

    .ql-toolbar {
      background: #242424;
      border-bottom: 1px solid #363636 !important;
      border-top: none !important;
      border-left: none !important;
      border-right: none !important;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }

    .ql-container {
      border: none !important;
      background: #1e1e1e;
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      min-height: 200px;
    }

    .ql-editor {
      color: #eaeaea;
      min-height: 200px;
    }

    .ql-editor.ql-blank::before {
      color: #666;
    }

    .ql-stroke {
      stroke: #eaeaea !important;
    }

    .ql-fill {
      fill: #eaeaea !important;
    }

    .ql-picker {
      color: #eaeaea !important;
    }

    .ql-picker-options {
      background-color: #242424 !important;
      border-color: #363636 !important;
    }

    .ql-active {
      background-color: #363636 !important;
    }

    .ql-toolbar button:hover,
    .ql-toolbar button:focus {
      background-color: #363636 !important;
    }
  `;

  const PaymentModal = () => {
    if (!showPaymentModal) return null;

    return (
      <div className="flex fixed inset-0 z-50 justify-center items-center">
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={() => !paymentLoading && setShowPaymentModal(false)}
        />

        <div className="relative z-50 w-full max-w-2xl rounded-lg bg-[#252525] p-6 shadow-xl">
          <div className="flex flex-col justify-center items-center">
            <CrownIcon />
            <h2 className="mt-4 text-center text-xl font-semibold text-[#eaeaea]">
              To Create a Community, Please Upgrade <br></br> Your Plan!
            </h2>
            <hr
              style={{
                width: "15%",
                height: "3px",
                border: "1px solid #7dd87d",
                backgroundColor: "#7dd87d",
                marginTop: "10px",
                marginBottom: "10px",
              }}
            ></hr>
          </div>

          <div className="mt-6 rounded-lg border border-[#363636] bg-[#1e1e1e] p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-sm font-medium text-[#7dd87d]">
                Premium Plan
              </h3>
              <div className="text-xl font-bold text-[#a3eca3]">$50.00</div>
            </div>

            <div className="space-y-3">
              <div className="flex gap-2 items-center">
                <svg
                  className="h-5 w-5 text-[#7dd87d]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                <span className="text-[#eaeaea]">
                  Create unlimited communities
                </span>
              </div>
              <div className="flex gap-2 items-center">
                <svg
                  className="h-5 w-5 text-[#7dd87d]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                <span className="text-[#eaeaea]">
                  Advanced community management tools
                </span>
              </div>
              <div className="flex gap-2 items-center">
                <svg
                  className="h-5 w-5 text-[#7dd87d]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                <span className="text-[#eaeaea]">
                  Priority customer support
                </span>
              </div>
              <div className="flex gap-2 items-center">
                <svg
                  className="h-5 w-5 text-[#7dd87d]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                <span className="text-[#eaeaea]">Analytics and reporting</span>
              </div>
              <p
                style={{ paddingTop: "20px" }}
                className="text-xs text-[#b5b5b5]"
              >
                Upgrade now to unlock all premium features and create your own
                thriving <br></br> community!
              </p>
            </div>
          </div>
          <div className="flex flex-col gap-3 mt-6">
            <div className="flex gap-2 items-center">
              <button
                onClick={() => handleCreateCommunity(false)}
                disabled={paymentLoading}
                className="w-full rounded-md bg-[#2e7d32] px-6 py-3 text-center text-xs font-semibold text-[#eaeaea] hover:bg-[#1b5e20] disabled:opacity-50"
              >
                {paymentLoading ? "Processing..." : "Upgrade Now"}
              </button>
              <button
                onClick={() => {
                  setShowPaymentModal(false);
                  navigate("/member/communities");
                }}
                disabled={paymentLoading}
                className="w-full rounded-md border border-[#363636] px-6 py-3 text-center text-xs font-semibold text-[#b5b5b5] hover:text-[#eaeaea] disabled:opacity-50"
              >
                Cancel
              </button>
            </div>
            <a
              href="#"
              className="mt-2 text-center text-sm text-[#7dd87d] hover:underline"
            >
              View all plan features
            </a>
          </div>
        </div>
      </div>
    );
  };

  // Add console log for initial form data
  useEffect(() => {
    console.log("Initial form data:", formData);
  }, []);

  // Add console log for form data changes
  useEffect(() => {
    console.log("Form data updated:", formData);
  }, [formData]);

  // Add console log for community members
  useEffect(() => {
    console.log("Community members updated:", communityMembers);
  }, [communityMembers]);

  return (
    <div className="min-h-screen bg-[#1e1e1e] p-6">
      <style>{editorStyles}</style>
      <div style={{}} className="mx-auto max-w-4xl">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="mb-2 text-2xl text-sm font-semibold text-[#eaeaea]">
              {pageTitle}
            </h1>
            <p className="text-[#b5b5b5]">
              Fill in the basic information to get started
            </p>
          </div>
          {isEditMode && (
            <button
              onClick={() => navigate(`/member/communities/${id}/requests`)}
              className="flex items-center gap-2 rounded-lg bg-[#2e7d32] px-6 py-2 text-sm text-white"
            >
              View Requests
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-5 h-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                <path
                  fillRule="evenodd"
                  d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          )}
        </div>
        {/* Tabs - Updated styling */}
        <div className="mb-8">
          <div className="flex">
            {[
              { id: "basic", label: "Basic Information" },
              { id: "description", label: "Community Description" },
              { id: "guidelines", label: "Community Guidelines" },
              { id: "privacy", label: "Privacy Settings" },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`relative px-2 py-2 text-sm ${
                  activeTab === tab.id ? "text-[#7dd87d]" : "text-[#b5b5b5]"
                }`}
              >
                {tab.label}
                {activeTab === tab.id && (
                  <div className="absolute bottom-0 left-0 h-0.5 w-full bg-[#7dd87d]" />
                )}
              </button>
            ))}
          </div>
          <div className="mt-[-1px] h-[1px] w-full bg-[#363636]" />
        </div>
      </div>

      <div
        style={{
          border: "1px solid #363636",
          borderRadius: "8px",
          padding: "16px",
        }}
        className="mx-auto max-w-4xl"
      >
        {/* Form Fields - Updated styling */}
        <div
          style={{
            width: "650px",
            marginLeft: "auto",
            marginRight: "auto",
          }}
          className="mb-8 rounded-lg  border-[#363636] bg-[#1e1e1e] p-8"
        >
          {activeTab === "basic" && renderBasicInfo()}
          {activeTab === "description" && renderDescription()}
          {activeTab === "guidelines" && renderGuidelines()}
          {activeTab === "privacy" && renderPrivacySettings()}
        </div>

        {/* Navigation - Updated styling */}
        <div className="flex justify-between">
          <button
            onClick={() => navigate("/member/communities")}
            className="rounded-lg border border-[#363636] bg-[#242424] px-6 py-2 text-sm text-[#eaeaea]"
          >
            Cancel
          </button>
          {activeTab === "privacy" ? (
            <button
              onClick={handleFormSubmit}
              className="flex items-center gap-2 rounded-lg bg-[#2e7d32] px-6 py-2 text-sm text-white"
            >
              {submitButtonText}
            </button>
          ) : (
            <button
              onClick={handleNextStep}
              className="flex items-center gap-2 rounded-lg bg-[#2e7d32] px-6 py-2 text-sm text-white"
            >
              Next Step
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          )}
        </div>
      </div>

      {!isEditMode && <PaymentModal />}
    </div>
  );
};

export default CreateCommunityPage;
